import { User } from '../user/User';
import { z } from 'zod';
import { AgentNamespace } from 'agents';
import { Connections } from '../agent/connection-agent';
import { ConnectionsWorkflowParams } from '../workflow/types';

export type Environment = {
  ENVIRONMENT: string;
  SUNNY_API_ENDPOINT: string;
  ANTHROPIC_API_KEY: string;
  GEMINI_API_KEY: string;
  CONNECTIONS_WORKFLOW: Workflow<ConnectionsWorkflowParams>;
  BROWSERBASE_PROJECT_ID: string;
  BROWSERBASE_API_KEY: string;
  KAKU_API_ENDPOINT: string;
  KAKU_WS_ENDPOINT: string;
  OPENAI_API_KEY: string;
  HYPERBROWSER_API_KEY: string;
  AI_GATEWAY_OPENAI_URL: string;
  AI_GATEWAY_ANTHROPIC_URL: string;
  AI_GATEWAY_GEMINI_URL: string;
  SKIP_CACHE: boolean;
  CACHE_VERSION_FACEBOOK?: string;
  CACHE_VERSION_GITHUB?: string;
  CACHE_VERSION_GOOGLE?: string;
  CACHE_VERSION_KAZEEL?: string;
  CACHE_VERSION_TEST?: string;
  CACHE_VERSION_LOGIN_TEST?: string;
  ICE_SERVERS: object[];
  Connections: AgentNamespace<Connections>;
  SCREENSHOTS_INBOUND_BUCKET: R2Bucket;
  TESTBROWSER: Fetcher;
};

export type KakuApp = {
  Bindings: Environment;
  Variables: {
    user?: User;
  };
};

export const TokenInfoSchema = z.object({
  xAuthToken: z.string(),
  csrfToken: z.string(),
});

export type TokenInfo = z.infer<typeof TokenInfoSchema>;
