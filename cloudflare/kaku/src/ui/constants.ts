export type PlatformTypes =
  | 'facebook'
  | 'github'
  | 'google'
  | 'kazeel'
  | 'test'
  | 'login_test'
  | 'text_captcha';

interface PlatformDetails {
  name: string;
  logo: string;
  loginLink: string;
}

export const platformDetails: Record<string, PlatformDetails> = {
  facebook: {
    name: 'Facebook',
    logo: '/fb.png',
    loginLink: 'https://facebook.com',
  },
  github: {
    name: 'Github',
    logo: '/github.png',
    loginLink: 'https://github.com/login',
  },
  google: {
    name: 'Google',
    logo: '/google.png',
    loginLink: 'https://accounts.google.com',
  },
  kazeel: {
    name: '<PERSON><PERSON><PERSON>',
    logo: '/kazeel-logo.png',
    loginLink: 'https://app.kazeel.com/a',
  },
  test: {
    name: 'Test',
    logo: '/google.png',
    loginLink: 'https://2captcha.com/demo/recaptcha-v2',
  },
  login_test: {
    name: 'Test',
    logo: '/google.png',
    loginLink: 'https://dev-tasks.kazeel.com/auth/login',
  },
  text_captcha: {
    name: 'Text Test',
    logo: '/fb.png',
    loginLink: 'https://2captcha.com/demo/normal',
  },
};
