/**
 * Core form interfaces for HTMX form generation
 */

export interface FormVisionResult {
  metadata: FormMetadata;
  controls: FormControls;
}

export interface FormMetadata {
  title: string;
  description: string;
  prompt: string | null;
  promptCode: string | null;
  errors?: string[];
  pageType: 'authenticated' | 'not-authenticated' | 'captcha' | 'loading' | 'other';
}

export interface FormControls {
  fields: FormField[];
  buttons: FormButton[];
}

export interface FormField {
  id: string;
  order: number;
  actor: 'ai' | 'human';
  label: string;
  type: 'text' | 'password' | 'email' | 'number' | 'checkbox' | 'radio' | 'textarea' | 'other';
  actiontype: 'fill' | 'click' | 'select';
  name: string;
  options?: { value: string; label: string }[] | undefined;
  readOnly: boolean;
}

export interface FormButton {
  id: string;
  order: number;
  actor: 'ai' | 'human';
  label: string;
  variant: 'primary' | 'secondary' | 'link';
  type: 'device-ack' | 'submit' | 'click';
  actiontype: 'click' | 'acknowledge';
  synthetic: boolean;
}
