import { WorkflowStepConfig } from 'cloudflare:workers';
import { PlatformTypes } from '../../ui/constants';

export const K_CUSTOM_VIEWPORT = { width: 1024, height: 768 };

/**
 * Platform-specific configuration for visual change detection
 * Handles different loading behaviors across platforms
 */
export interface PlatformDetectionConfig {
  changeThreshold: number;
  checkInterval: number;
  maxWaitTime: number;
  stabilityChecks?: number;
  minChangeDetectionDelay?: number;
}

export const platformDetectionConfigs: Record<PlatformTypes, PlatformDetectionConfig> = {
  facebook: {
    changeThreshold: 5, // Higher threshold for Facebook's intermediate screens
    checkInterval: 500, // More frequent checks
    maxWaitTime: 15000, // Longer timeout for complex loading
    stabilityChecks: 4, // Require 4 stable checks
    minChangeDetectionDelay: 2500, // Wait 2.5s before accepting changes
  },
  google: {
    changeThreshold: 1.5,
    checkInterval: 1000,
    maxWaitTime: 10000,
    stabilityChecks: 2,
    minChangeDetectionDelay: 1000,
  },
  github: {
    changeThreshold: 2,
    checkInterval: 1000,
    maxWaitTime: 10000,
    stabilityChecks: 2,
  },
  kazeel: {
    changeThreshold: 1.5,
    checkInterval: 1000,
    maxWaitTime: 10000,
    stabilityChecks: 2,
  },
  test: {
    changeThreshold: 1.5,
    checkInterval: 1000,
    maxWaitTime: 10000,
    stabilityChecks: 2,
  },
  login_test: {
    changeThreshold: 1.5,
    checkInterval: 1000,
    maxWaitTime: 10000,
    stabilityChecks: 2,
  },
  text_captcha: {
    changeThreshold: 1.5,
    checkInterval: 1000,
    maxWaitTime: 10000,
    stabilityChecks: 2,
  },
};

export const defaultWorkflowNoRetryConfig: WorkflowStepConfig = {
  retries: {
    limit: 0,
    delay: '10 seconds',
    backoff: 'exponential',
  },
  timeout: '15 minutes',
};

export const defaultWorkflowRetryConfig: WorkflowStepConfig = {
  retries: {
    limit: 2,
    delay: '0 seconds',
    backoff: 'linear',
  },
  timeout: '15 minutes',
};

// Default platform versions (fallback values)
export const defaultPlatformVersions = {
  facebook: 'v1.0.3',
  github: 'v1.0.3',
  google: 'v1.0.6',
  kazeel: 'v1.0.3',
  test: 'v1.0.3',
  text_captcha: 'v1.0.3',
  login_test: 'v1.0.4',
} as const;

// Legacy export for backward compatibility
export const platformVersions = defaultPlatformVersions;

/**
 * Get platform version from environment variables with fallback to default values
 * This enables dynamic cache version management via environment variables/secrets
 */
export function getPlatformVersion(platform: PlatformTypes, env?: any): string {
  if (!env) {
    return defaultPlatformVersions[platform];
  }

  // Map platform names to environment variable names
  const envVarMap: Record<PlatformTypes, string> = {
    facebook: 'CACHE_VERSION_FACEBOOK',
    github: 'CACHE_VERSION_GITHUB',
    google: 'CACHE_VERSION_GOOGLE',
    kazeel: 'CACHE_VERSION_KAZEEL',
    test: 'CACHE_VERSION_TEST',
    text_captcha: 'CACHE_VERSION_TEXT_CAPTCHA',
    login_test: 'CACHE_VERSION_LOGIN_TEST',
  };

  const envVarName = envVarMap[platform];
  const envVersion = env[envVarName];

  // Return environment version if available, otherwise fallback to default
  return envVersion || defaultPlatformVersions[platform];
}

export const htmxPromptInstructions = `
Generate a HTMX fragment that renders only the controls needed to complete this form.

Return ONLY the HTMX text (no Markdown, no commentary).
### HTMX Form Requirements:
- Main form element: <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
- Primary Login button MUST be a <button> element and MUST include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click". This is a critical requirement.
- ALL other buttons must also include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML".
- ALL input fields in the generated form must be empty even if the screenshot shows them filled
- CRITICAL: Do not include the title or main page description text on the form, ONLY input fields and login/submit buttons
- For input fields, the name should be the placeholder used, written in snake case.
- In the case of no input fields return an empty response

### CRITICAL: Error Message Detection
- Look for text near input fields or buttons, especially text with a warning icon (!) or text in a different color like red or orange.
- If any validation errors are visible, add each error message to the "errors" array and include a <div class="form-error"> containing the message. This div must be placed directly before the .button-container for the primary action button.

### CSS Classes (use these exact classes):
- form-container
- input-container: wrapper for all input types
- **FLOATING LABEL INPUTS (preferred for text/password/email inputs):**
  - floating-input-wrapper: wrapper div for floating label inputs
  - floating-input: the input element (use instead of input-field)
  - floating-label: the label element (use instead of form-label)
  - Structure: <div class="input-container"><div class="floating-input-wrapper"><input class="floating-input" name="" placeholder="" type=""><label class="floating-label"></div></div>
- **LEGACY INPUTS (only for special cases):**
  - form-label, input-field: traditional label above input
- select-container, select-field
- checkbox-container, checkbox-field, checkbox-label
- radio-container, radio-option, radio-field
- button-container, button-primary, button-secondary
- form-error, otp-container, otp-input
- form-section, form-section-title

### Input Field Guidelines:
- **USE traditional labels for:** checkboxes, radio buttons, select dropdowns
- **ALWAYS set placeholder="" (empty) for floating inputs**
- **ALWAYS include proper for/id attributes for accessibility**

### Example Floating Label Structure:
For username input: input-container > floating-input-wrapper > (floating-input + floating-label)
For password input: input-container > floating-input-wrapper > (floating-input + floating-label)
Remember: floating-input must have placeholder="" and floating-label must come after the input

### Exclusion Rules:
- Exclude all secondary or alternative actions on the form generated.
- Do not include "Forgot Password?", or "Reset Password" links/buttons.
- Do not include "Create Account" or "Sign Up" links/buttons.
- Do not include "Remember me" or "Stay signed in" checkboxes.
- Do not include Social Login options or any non-login links/buttons.

### Critical for 2FA and 2-Step Verification pages:
- Always add a manual 'Acknowledged' button in the form if the primary 2FA flow lacks an explicit confirmation button.
- Ensure any visible 2-factor authentication codes are clearly highlighted within the form. Use a larger font size or bold styling to emulate the platform's emphasis.
- Add labels or secondary description from the screenshot and remove checkboxes if present.
`;

export const pageDetailsPromptInstructions = `
Generate one valid, self-contained JSON object describing a single, currently visible login (or auth) form.

Return ONLY raw JSON (no Markdown, no commentary).

Before responding, validate that your JSON conforms to the schema below; if invalid, fix automatically.

The formTitle and formDescription must describe the form itself, its intent, key fields, or challenge type—not any downstream effects of submitting it (e.g., gaining account access, redirecting, unlocking content)

Required JSON format:
{
  "formTitle": "string", // short heading for the whole form
  "formDescription": "string", // high-level task summary (e.g. "Sign in with email and password")
  "errors": string[], // include visible validation messages
  "pageType": "credentials" | "otp" | "captcha" | "2-factor-auth" | "authenticated" | "other" | "loading",
  "actions": [
    {
      "type": "click" | "fill" | "acknowledge",
      "name": "string",
      "value": "string",
      "coordinates": {"x": number, "y": number},
      "order": number,
      "isSubmitAction": boolean
    }
  ]
}
- CRITICAL: Verify all critical login details are covered across the formTitle or formDescription.

### CRITICAL: PAGE TYPE DETERMINATION RULES (MUST FOLLOW EXACTLY)

**PRIORITY ORDER (check in this exact sequence):**

1. **"authenticated"** - HIGHEST PRIORITY
   - User is already logged in and viewing authenticated content
   - Dashboard, profile, or main app interface is visible
   - User avatar, name, or account info displayed
   - Post-login navigation menus visible
   - If ANY authenticated indicators present → STOP and return "authenticated"

2. **"loading"** - SECOND PRIORITY
   - Page is actively loading with spinners/progress indicators
   - Blank or partially rendered content
   - If loading state detected → STOP and return "loading"

3. **FORM TYPES** - ONLY if NOT authenticated/loading:
   - "credentials": Username/email + password fields
   - "otp": One-time password or verification code input
   - "2-factor-auth": Two-factor authentication challenge
   - "captcha": CAPTCHA is the PRIMARY blocking element (not secondary)
   - "other": Unclear or mixed authentication state

### Actions Array:
- Order by visual position (top to bottom, 0-based)
- Fill actions: type="fill", include name and coordinates. Name should be the placeholder used for that input in snake case.
- Click actions: type="click", include coordinates
- Submit actions: isSubmitAction=true
- In the case the form lacks an explicit confirmation button, add an action of type acknowledge

### Exclusion Rules:
- Exclude all secondary or alternative actions from the actions array.

Set errors=[] unless validation messages visible.
`;

export const monolithicFormPromptInstructions = `
Generate one valid, self-contained JSON object describing a single, currently visible login (or auth) form and an embedded HTMX fragment that renders only the controls needed to complete that form.

Return ONLY raw JSON (no Markdown, no commentary).

Before responding, validate that your JSON conforms to the schema below; if invalid, fix automatically.

The formTitle and formDescription must describe the form itself, its intent, key fields, or challenge type—not any downstream effects of submitting it (e.g., gaining account access, redirecting, unlocking content)

Required JSON format:
{
  "formTitle": "string", // short heading for the whole form
  "formDescription": "string", // high-level task summary (e.g. "Sign in with email and password")
  "errors": string[], // include visible validation messages
  "pageType": "credentials" | "otp" | "captcha" | "2-factor-auth" | "authenticated" | "other" | "loading" | "error",
  "htmxForm": "string",
  "actions": [
    {
      "type": "click" | "fill" | "acknowledge",
      "name": "string",
      "value": "string",
      "coordinates": {"x": number, "y": number},
      "order": number,
      "isSubmitAction": boolean
    }
  ]
}

### HTMX Form Requirements:
- Main form element: <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
- Primary Login button MUST be a <button> element and MUST include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click". This is a critical requirement.
- ALL other buttons must also include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML".
- ALL input fields in the HTMX form must be empty even if the screenshot shows them filled
- CRITICAL: Ensure formTitle and formDescription content of the JSON Parameters aren’t duplicated in HTMX. Verify all critical login details are covered across the title, description, or HTMX, as the title and description renders first and then we render the HTMX form.

### CRITICAL: Error Message Detection
- Look for text near input fields or buttons, especially text with a warning icon (!) or text in a different color like red or orange.
- If any validation errors are visible, add each error message to the "errors" array and include a <div class="form-error"> containing the message. This div must be placed directly before the .button-container for the primary action button.


### CRITICAL: PAGE TYPE DETERMINATION RULES (MUST FOLLOW EXACTLY)

Before doing ANYTHING else, you MUST determine if the user is already authenticated.

Look for these AUTHENTICATED PAGE indicators:
- User dashboard, avatar, name, or account information displayed
- Navigation menus showing logged-in user options

If ANY authenticated indicators are present → IMMEDIATELY return pageType "authenticated" and STOP.
**CRITICAL: Do NOT generate forms for authenticated pages. If user is logged in, return pageType "authenticated" with empty htmxForm and actions array.**

### CSS Classes (use these exact classes):
- form-container
- input-container: wrapper for all input types
- **FLOATING LABEL INPUTS (preferred for text/password/email inputs):**
  - floating-input-wrapper: wrapper div for floating label inputs
  - floating-input: the input element (use instead of input-field)
  - floating-label: the label element (use instead of form-label)
  - Structure: <div class="input-container"><div class="floating-input-wrapper"><input class="floating-input" name="" placeholder="" type=""><label class="floating-label"></div></div>
- **LEGACY INPUTS (only for special cases):**
  - form-label, input-field: traditional label above input
- select-container, select-field
- checkbox-container, checkbox-field, checkbox-label
- radio-container, radio-option, radio-field
- button-container, button-primary, button-secondary
- form-error, otp-container, otp-input
- form-section, form-section-title

### Input Field Guidelines:
- **USE traditional labels for:** checkboxes, radio buttons, select dropdowns
- **ALWAYS set placeholder="" (empty) for floating inputs**
- **ALWAYS include proper for/id attributes for accessibility**

### Example Floating Label Structure:
For username input: input-container > floating-input-wrapper > (floating-input + floating-label)
For password input: input-container > floating-input-wrapper > (floating-input + floating-label)
Remember: floating-input must have placeholder="" and floating-label must come after the input

### Actions Array:
- Order by visual position (top to bottom, 0-based)
- Fill actions: type="fill", include name and coordinates
- Click actions: type="click", include coordinates
- Submit actions: isSubmitAction=true
- Include: ONLY input fields and login/submit buttons
- In the case the form lacks an explicit confirmation button, add an acknowledged button with the type acknowledge 

### Exclusion Rules:
- Exclude all secondary or alternative actions from both the htmxForm and the actions array.
- Do not include "Forgot Password?", or "Reset Password" links/buttons.
- Do not include "Create Account" or "Sign Up" links/buttons.
- Do not include "Remember me" or "Stay signed in" checkboxes.
- Do not include Social Login options or any non-login links/buttons.

### Critical for 2FA and 2-Step Verification pages:
- Always add a manual 'Acknowledged' button in the htmxForm and actions array if the primary 2FA flow lacks an explicit confirmation button.
- Ensure any visible 2-factor authentication codes are clearly highlighted within the HTMX form. Use a larger font size or bold styling to emulate the platform's emphasis.
- Add labels/description from the form and remove checkboxes if present.

### CRITICAL for Passkey Authentication Pages:
When you detect a page where the PRIMARY authentication method is passkey/WebAuthn (showing "Use your passkey", "Touch ID", "Face ID", "Windows Hello", "Security key", "Use your phone", QR codes), you MUST:

1. **ONLY include alternative authentication options** in both htmxForm and actions array
2. **Include alternative options like:**
   - "Sign in with password" links/buttons
   - "Other sign-in options" buttons
   - "Try another way" links
   - "Use password instead" buttons
3. **COMPLETELY EXCLUDE primary passkey/WebAuthn elements** from htmxForm and actions
4. **Use descriptive formTitle and formDescription** explaining that passkey is not supported and the alternative options available

Set errors=[] unless validation messages visible.
`;

export const formGeneratorPromptInstructions = `
Return one **valid JSON** object describing a visible **login/auth** form with an **HTMX fragment** that renders only the required controls.

**Return ONLY raw JSON** (no markdown, no commentary).

---
### CRITICAL RULES (MUST OBEY)

1. **Authenticated pages**: If user is logged in (dashboard, avatar, account menu), return:
   - pageType: "authenticated"
   - htmxForm: ""
   - actions: []

2. **Exclude ALL secondary/alternate flows**:
   - "Forgot Password", "Reset", "Create Account", "Sign Up", "Show password"
   - "Remember Me", "Stay signed in", social login, other links
   - Include only one direct login path

3. **2FA/OTP pages**:
   - If no explicit confirm/submit button is visible for 2-FA Submission, the htmx form must include an Acknowledge labelled button with relevant formTitle and formDescription
   - Highlight any displayed code input

4. **Passkey/WebAuthn pages**:
   - Do NOT include primary passkey UI (Touch/Face ID, QR)
   - Only include fallback controls (e.g. "Use password instead", "Try another way")
   
5. Exclude readonly fields and preference checkboxes on 2FA prompt pages*

  - If the page includes a readonly input (e.g., pre-filled email), **do not include it** in the \`htmxForm\` or actions array.
  - Do not include checkboxes for device or session preferences (e.g., “Don’t ask again”, “Remember this device”).
  - Only include an \`acknowledge\` action when no inputs or submit buttons are present and the page instructs the user to verify on another device.

---
### JSON FORMAT
{
  "formTitle": "string",
  "formDescription": "string",
  "errors": string[],
  "pageType": "credentials" | "otp" | "captcha" | "2-factor-auth" | "authenticated" | "other" | "loading" | "error",
  "htmxForm": "string",
  "actions": [
    {
      "type": "click" | "fill" | "acknowledge",
      "name": "string",
      "value": "string",
      "order": number,
      "isSubmitAction": boolean
    }
  ]
}

---
### PAGE TYPE RULES
1. Check for authentication → "authenticated"
2. Else choose in order: "credentials", "otp" or "2-factor-auth", "captcha", "loading", "error", "other"

---
### HTMX FORM STRUCTURE
- Root: <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
- Primary submit (if present): <button class="button-primary" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click">
- Inputs must be empty, semantic, accessible, use placeholder="" for floating inputs
- Floating‑label pattern:
  <div class="input-container">
    <div class="floating-input-wrapper">
      <input class="floating-input" name="" type="" placeholder="" required>
      <label class="floating-label"></label>
    </div>
  </div>
- Use legacy form-label + input-field only if needed
- Include required, pattern attributes as visible

### CSS Classes (use these exact classes):
- form-container
- input-container: wrapper for all input types
- **FLOATING LABEL INPUTS (preferred for text/password/email inputs):**
  - floating-input-wrapper: wrapper div for floating label inputs
  - floating-input: the input element (use instead of input-field)
  - floating-label: the label element (use instead of form-label)
  - Structure: <div class="input-container"><div class="floating-input-wrapper"><input class="floating-input" name="" placeholder="" type=""><label class="floating-label"></div></div>
- **LEGACY INPUTS (only for special cases):**
  - form-label, input-field: traditional label above input
- select-container, select-field
- checkbox-container, checkbox-field, checkbox-label
- radio-container, radio-option, radio-field
- button-container, button-primary, button-secondary
- form-error, otp-container, otp-input
- form-section, form-section-title

---
### ACTIONS ARRAY
- One entry per control, in visual order
- type: fill for inputs, click for buttons, acknowledge for manual confirmation when no submit exists
- isSubmitAction: true only on primary submit or fallback ack
- Exclude any non-main-path controls

**Only include one button in the output**
* If the page contains multiple buttons, **select only one representative button** — the most relevant or primary action, which MUST be about login.
* **Exception:** If the buttons correspond to **different login options** (e.g., "Login with Google", "Login with Facebook"), include all of them.
* In all other cases, **DO NOT include multiple buttons. Focus on a single one.**

---
### DEFAULT
Set errors: [] unless visible validation messages exist
`;
